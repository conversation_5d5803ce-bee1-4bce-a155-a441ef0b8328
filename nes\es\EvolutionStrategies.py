import torch

class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, models, policy, optimizer, device, sigma, num_env_steps_per_update):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.agents = models
        self.policy = policy
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update

    def get_flat_params(self, model):
        """
        
        """
        return torch.cat([p.data.view(-1) for p in model.parameters()])
    
    def set_flat_params(self, model, flat_params):
        """
        Set the parameters of the model to the values in the flat vector
        Given a new flat_params vector of length 16:
        - First 12 elements → reshape to (4x3) and copy into weight
        - Next 4 elements → reshape to (4,) and copy into bias
        """

        prev_ind = 0
        for p in model.parameters():
            flat_size = p.numel()
            p.data.copy_(flat_params[prev_ind:prev_ind + flat_size].view_as(p))
            prev_ind += flat_size

    def sample_noise(self):
        noise_list = []
        for p in self.policy.parameters():
            noise_list.append(torch.randn(self.num_agents, *p.shape, device=self.device)) #creates 3D tensor of noise, matching the shape of the params
        return noise_list
    
    def perturbate(self, policy, noise_list):
        perturbated_params = []
        for p, n in zip(self.policy.parameters(), noise_list):
            perturbated_params.append(p + self.sigma * n) #shape: (num_agents, *p.shape)
        return perturbated_params

    def run_rollout(self, policy):
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent
        done = torch.zeros(self.num_agents, dtype=torch.bool, device=self.device)

        #---- rollout ----
        for _ in range(self.num_env_steps):
                
            # get actions from policy, reacting to observation
            actions = self.policy(obs)

            # step the enviroment, to let the agents execute the actions
            obs, rewards, dones, extras = self.env.step(actions)

            # move bew obs/rewards to GPU if it isnt already
            obs = obs.to(self.device)
            total_rewards = total_rewards.to(self.device)

            # calculate rewards for agents, that are still alive
            total_rewards += rewards * (~done) #use ~ to invert the done mask and only add rewards for agents that are still alive, since false = 0 & true = 1

            #update done mask
            done |= dones

            #break if all agents are done
            if done.all():
                break

        return total_rewards

    def run(self):
        for iteration in range(self.iterations):

            #--- perturb agent params ---
            param_size = self.get_flat_params(self.policy).shape[0] #length of the flattened tensor
            noise = torch.randn(self.num_agents, param_size, device=self.device)
            base_parmas = self.get_flat_params(self.policy).to(self.device)#flattened tensor
            perturbated_params = base_parmas.unsqueeze(0) + self.sigma * noise #2D tensor of shape (num_agents, param_size)

            #need differnet approach that for loop
            for i in range(self.num_agents): 
                self.set_flat_params(self.policy, perturbated_params[i]) #set the parameters of the policy to the perturbated parameters
                total_rewards[i] = self.run_rollout(self.policy)

            

            

"""
class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, models, policy, optimizer, device, sigma, num_env_steps_per_update):
        try:
            print("ES is initializing...")
            self.iterations = iterations
            self.num_agents = num_agents
            self.env = env
            self.agents = models
            self.policy = policy
            self.optimizer = optimizer
            self.device = device
            self.sigma = sigma
            self.num_env_steps = num_env_steps_per_update
            print("ES initialized")
        except Exception as e:
            print(f"ES failed to initialize: {e}")
            import traceback
            print(traceback.format_exc())

    def run(self):
        print("runs being called")

        param_dim = self.policy.get_params().shape[0]

        for iteration in range(self.iterations):
            # 1. Sample noise matrix: shape (num_agents, param_dim)
            noises = torch.randn(self.num_agents, param_dim, device=self.device)

            # 2. Perturb each agent's policy params with noise * sigma
            for i, agent in enumerate(self.agents):
                agent.perturb(noises[i].cpu().numpy(), self.sigma)

            # 3. Rollout each agent and get their rewards
            rewards = []
            for agent in self.agents:
                r = agent.rollout(self.num_env_steps)  # returns vector of rewards over batch envs
                rewards.append(r.mean())  # average reward over batch envs per agent
            rewards = torch.tensor(rewards, device=self.device)

            # 4. Normalize rewards (optional but recommended)
            rewards = (rewards - rewards.mean()) / (rewards.std() + 1e-8)

            # 5. Update master policy parameters using optimizer step
            self.optimizer.step(rewards, noises)

            # 6. Sync updated master policy params to all agents
            updated_params = self.policy.get_params()
            for agent in self.agents:
                agent.policy.set_params(updated_params.cpu())

            # 7. (Optional) Logging
            print(f"Iteration {iteration}: mean reward = {rewards.mean().item():.4f}")
"""
