import torch

class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, models, policy, optimizer, device, sigma, num_env_steps_per_update):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.agents = models
        self.policy = policy
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update

    def get_flat_params(self, model):
        """
        Flatten a single agent slice (index 0) for tensors with a leading
        dimension equal to num_agents; otherwise flatten as usual. This makes
        ES optimize a single shared parameter vector.
        """
        flats = []
        for p in model.parameters():
            if p.dim() > 0 and p.size(0) == self.num_agents:
                flats.append(p.data[0].reshape(-1))
            else:
                flats.append(p.data.reshape(-1))
        return torch.cat(flats)

    def set_flat_params(self, model, flat_params):
        """
        Set parameters from a single flat vector, replicating values across the
        agent dimension when a tensor has leading dim == num_agents. Ensures all
        envs use identical weights within a rollout.
        """
        prev_ind = 0
        for p in model.parameters():
            if p.dim() > 0 and p.size(0) == self.num_agents:
                flat_size = p[0].numel()
                slice0 = flat_params[prev_ind:prev_ind + flat_size].view_as(p[0])
                p.data.copy_(slice0.unsqueeze(0).expand_as(p.data))
                prev_ind += flat_size
            else:
                flat_size = p.numel()
                p.data.copy_(flat_params[prev_ind:prev_ind + flat_size].view_as(p))
                prev_ind += flat_size

    def sample_noise(self):
        noise_list = []
        for p in self.policy.parameters():
            noise_list.append(torch.randn(self.num_agents, *p.shape, device=self.device)) #creates 3D tensor of noise, matching the shape of the params
        return noise_list
    
    def perturbate(self, policy, noise_list):
        perturbated_params = []
        for p, n in zip(self.policy.parameters(), noise_list):
            perturbated_params.append(p + self.sigma * n) #shape: (num_agents, *p.shape)
        return perturbated_params

    def run_rollout(self):
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent
        done = torch.zeros(self.num_agents, dtype=torch.bool, device=self.device)

        #---- rollout ----
        with torch.no_grad():
            for _ in range(self.num_env_steps):
                # get actions from policy, reacting to observation
                actions = self.policy(obs)

                # step the environment, to let the agents execute the actions
                obs, rewards, dones, extras = self.env.step(actions)

                # ensure tensors are on the correct device
                obs = obs.to(self.device)
                rewards = rewards.to(self.device)
                dones = dones.to(self.device)

                # calculate rewards for agents that are still alive
                total_rewards += rewards * (~done)  # ~ to invert the done mask

                # update done mask
                done |= dones

                # break if all agents are done
                if done.all():
                    break

        return total_rewards

    def run(self):
        for _ in range(self.iterations):

            #--- perturb agent params ---
            param_size = self.get_flat_params(self.policy).shape[0]  # length of the flattened tensor
            noises = torch.randn(self.num_agents, param_size, device=self.device)
            base_params = self.get_flat_params(self.policy).to(self.device)  # flattened tensor
            perturbated_params = base_params.unsqueeze(0) + self.sigma * noises  # (num_agents, param_size)

            total_rewards = torch.zeros(self.num_agents, device=self.device)

            #--- run rollout for each perturbated param vector/agent ---
            for i in range(self.num_agents):
                self.set_flat_params(self.policy, perturbated_params[i])  # set perturbed params
                r = self.run_rollout()
                total_rewards[i] = r.mean()

            # Restore base parameters before update
            self.set_flat_params(self.policy, base_params)

            #--- normalize rewards and update via optimizer ---
            rewards = (total_rewards - total_rewards.mean()) / (total_rewards.std() + 1e-8)
            self.optimizer.step(rewards, noises)
