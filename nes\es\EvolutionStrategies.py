import torch

class EvolutionStrategies:
    def __init__(self, iterations, num_agents, env, models, policy, optimizer, device, sigma, num_env_steps_per_update):
        self.iterations = iterations
        self.num_agents = num_agents
        self.env = env
        self.agents = models
        self.policy = policy
        self.optimizer = optimizer
        self.device = device
        self.sigma = sigma
        self.num_env_steps = num_env_steps_per_update

    def get_flat_params(self, model):
        """
        
        """
        return torch.cat([p.data.view(-1) for p in model.parameters()])
    
    def set_flat_params(self, model, flat_params):
        """
        Set the parameters of the model to the values in the flat vector
        Given a new flat_params vector of length 16:
        - First 12 elements → reshape to (4x3) and copy into weight
        - Next 4 elements → reshape to (4,) and copy into bias
        """

        prev_ind = 0
        for p in model.parameters():
            flat_size = p.numel()
            p.data.copy_(flat_params[prev_ind:prev_ind + flat_size].view_as(p))
            prev_ind += flat_size

    def sample_noise(self):
        noise_list = []
        for p in self.policy.parameters():
            noise_list.append(torch.randn(self.num_agents, *p.shape, device=self.device)) #creates 3D tensor of noise, matching the shape of the params
        return noise_list
    
    def perturbate(self, policy, noise_list):
        perturbated_params = []
        for p, n in zip(self.policy.parameters(), noise_list):
            perturbated_params.append(p + self.sigma * n) #shape: (num_agents, *p.shape)
        return perturbated_params

    def run_rollout(self):
        #---reset envs ---
        obs, _ = self.env.reset()
        obs = obs.to(self.device) #initial observation

        total_rewards = torch.zeros(self.num_agents, device=self.device) #1D tensor with reward per agent
        done = torch.zeros(self.num_agents, dtype=torch.bool, device=self.device)

        #---- rollout ----
        with torch.no_grad():
            for _ in range(self.num_env_steps):
                # get actions from policy, reacting to observation
                actions = self.policy(obs)

                # step the environment, to let the agents execute the actions
                obs, rewards, dones, extras = self.env.step(actions)

                # ensure tensors are on the correct device
                obs = obs.to(self.device)
                rewards = rewards.to(self.device)
                dones = dones.to(self.device)

                # calculate rewards for agents that are still alive
                total_rewards += rewards * (~done)  # ~ to invert the done mask

                # update done mask
                done |= dones

                # break if all agents are done
                if done.all():
                    break

        return total_rewards

    def run(self):
        for iteration in range(self.iterations):

            #--- perturb agent params ---
            param_size = self.get_flat_params(self.policy).shape[0]  # length of the flattened tensor
            noise = torch.randn(self.num_agents, param_size, device=self.device)
            base_params = self.get_flat_params(self.policy).to(self.device)  # flattened tensor
            perturbated_params = base_params.unsqueeze(0) + self.sigma * noise  # (num_agents, param_size)

            # Evaluate each perturbation; without batched-params support in the policy,
            # we must roll out one set of params at a time. The vectorized env acts as
            # parallel replicas to reduce variance. We aggregate to a scalar per agent.
            total_rewards = torch.zeros(self.num_agents, device=self.device)

            for i in range(self.num_agents):
                self.set_flat_params(self.policy, perturbated_params[i])  # set perturbed params
                r = self.run_rollout()  # per-env rewards for this parameter vector
                total_rewards[i] = r.mean()

            # Restore base parameters after evaluation
            self.set_flat_params(self.policy, base_params)
            

