import argparse
import os

import pickle
import shutil

import torch
import genesis as gs

from drone.config import get_cfgs
from drone.env import HoverEnv
from es import get_models, SGD, EvolutionStrategies



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--exp_name", type=str, default="target")
    parser.add_argument("-B", "--num_envs", type=int, default=1)
    args = parser.parse_args()

    gs.init(logging_level="warning")

    log_dir = f"logs/{args.exp_name}"
    env_cfg, obs_cfg, reward_cfg, command_cfg = get_cfgs()

    if os.path.exists(log_dir):
        shutil.rmtree(log_dir)
    os.makedirs(log_dir, exist_ok=True)

    pickle.dump(
        [env_cfg, obs_cfg, reward_cfg, command_cfg],
        open(f"{log_dir}/cfgs.pkl", "wb"),
    )

    env = HoverEnv(
        num_envs=args.num_envs,
        env_cfg=env_cfg,
        obs_cfg=obs_cfg,
        reward_cfg=reward_cfg,
        command_cfg=command_cfg,
        show_viewer=True,
    )

    sigma = 0.1
    learning_rate = 0.01
    num_env_steps_per_update = 32
    iterations = 10
    hidden_dim = 64
    num_hidden = 2


    policy = get_models(
        num_agents=args.num_envs,
        input_dim=obs_cfg["num_obs"],
        output_dim=env_cfg["num_actions"],
        hidden_dim=hidden_dim,
        num_hidden=num_hidden,
        device=gs.device
    )

    print(f"Policy is running on device: {next(policy.parameters()).device}")
    print(f"Model architecture: {policy}")   

    optimizer = SGD(
        policy=policy,
        num_agents=args.num_envs,
        sigma=sigma,
        learning_rate=learning_rate,
        device = gs.device
    )

    #print("optimizer working")

    evo = EvolutionStrategies(
        iterations=iterations,
        num_agents=args.num_envs,
        env=env,
        models=agents,
        policy=policy,
        optimizer=optimizer,
        device=gs.device,
        sigma=sigma,
        num_env_steps_per_update=num_env_steps_per_update,
    )

    evo.run()

if __name__ == "__main__":
    torch.manual_seed(seed=0)
    torch.set_float32_matmul_precision("high")
    main()

"""
PLAN:
a loop that:

Resets the environment to get initial observations. DONE

Runs the policy to get actions. 

Steps the environment with those actions.

Calculates a reward signal for each agent.

Updates the policy parameters based on those rewards.

For Evolution Strategies (ES), that means:

Create noise samples for each agent’s parameters.

Apply them to the batched network in one go.

Run a rollout for all agents in parallel.

Compute fitness (total rewards).

Update parameters in the batched network using ES formula.
"""
