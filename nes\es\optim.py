import torch
class SGD:
    def __init__(self, policy, learning_rate, sigma, num_agents, device):
        self.policy = policy
        self.learning_rate = learning_rate
        self.sigma = sigma
        self.num_agents = num_agents
        self.device = device
        self.flat_params = self.get_flat_params().to(self.device)

    def get_flat_params(self):
        return torch.cat([p.data.view(-1) for p in self.policy.net.parameters()])
    
    def set_flat_params(self, flat_params):
        idx = 0
        for p in self.policy.net.parameters():
            size = p.numel()
            p.data.copy_(flat_params[idx: idx + size].view_as(p))
            idx += size

    def step(self, rewards, noises):
        if not isinstance(rewards, torch.Tensor):
            rewards = torch.tensor(rewards, device=self.device)
        if not isinstance(noises, torch.Tensor):
            noises = torch.tensor(noises, device=self.device)

        gradient = (rewards @ noises) / (self.num_agents * self.sigma)

        # Update flat_params
        self.flat_params = self.flat_params + self.learning_rate * gradient.to(self.device)

        # Set updated params back to policy
        self.set_flat_params(self.flat_params)
