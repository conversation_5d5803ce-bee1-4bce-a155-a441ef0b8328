import torch

class SGD:
    def __init__(self, policy, learning_rate, sigma, num_agents, device):
        self.policy = policy
        self.learning_rate = learning_rate
        self.sigma = sigma
        self.num_agents = num_agents
        self.device = device
        self.flat_params = self.get_flat_params().to(self.device)

    def get_flat_params(self):
        """
        Flatten only a single agent slice (index 0) for parameters that have a
        leading dimension equal to num_agents. This ensures ES optimizes a
        single shared parameter vector and we replicate to all agents on set.
        """
        flats = []
        for p in self.policy.net.parameters():
            if p.dim() > 0 and p.size(0) == self.num_agents:
                flats.append(p.data[0].reshape(-1))
            else:
                flats.append(p.data.reshape(-1))
        return torch.cat(flats)

    def set_flat_params(self, flat_params):
        """
        Set parameters from a single flat vector by copying into slice 0, then
        replicating that slice across the agent dimension for tensors that have
        leading dim == num_agents.
        """
        idx = 0
        for p in self.policy.net.parameters():
            if p.dim() > 0 and p.size(0) == self.num_agents:
                size0 = p[0].numel()
                slice0 = flat_params[idx: idx + size0].view_as(p[0])
                # replicate slice0 to all agents
                p.data.copy_(slice0.unsqueeze(0).expand_as(p.data))
                idx += size0
            else:
                size = p.numel()
                p.data.copy_(flat_params[idx: idx + size].view_as(p))
                idx += size

    def step(self, rewards, noises):
        if not isinstance(rewards, torch.Tensor):
            rewards = torch.tensor(rewards, device=self.device)
        if not isinstance(noises, torch.Tensor):
            noises = torch.tensor(noises, device=self.device)

        # ES gradient estimate: g = (1/(N*sigma)) * sum_i r_i * eps_i
        gradient = (rewards @ noises) / (self.num_agents * self.sigma)

        # Update flat_params
        self.flat_params = self.flat_params + self.learning_rate * gradient.to(self.device)

        # Set updated params back to policy (replicated across agents)
        self.set_flat_params(self.flat_params)
